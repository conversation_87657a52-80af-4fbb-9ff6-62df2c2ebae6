from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    is_cigar = fields.<PERSON><PERSON>an(
        string='Is Premium Cigar',
        default=False,
        help='Check this box if this product is a premium cigar (American standard: measured in boxes of 25 cigars)'
    )

    is_cigarette = fields.<PERSON><PERSON><PERSON>(
        string='Is Cigarette',
        default=False,
        help='Check this box if this product is a cigarette (American standard: measured in packs of 20 cigarettes)'
    )

    is_tobacco = fields.Boolean(
        string='Is Tobacco',
        default=False,
        help='Check this box if this product is tobacco (American standard: measured in fluid ounces)'
    )

    @api.onchange('is_cigar')
    def _onchange_is_cigar(self):
        """When premium cigar is selected, uncheck others and set UOM to boxes (American standard)"""
        if self.is_cigar:
            self.is_cigarette = False
            self.is_tobacco = False
            # Use the predefined box UOM (American standard: 25 premium cigars per box)
            box_uom = self.env.ref('ai_ci_to_invoice.uom_box', raise_if_not_found=False)
            if box_uom:
                self.uom_id = box_uom.id
                self.uom_po_id = box_uom.id

    @api.onchange('is_cigarette')
    def _onchange_is_cigarette(self):
        """When cigarette is selected, uncheck others and set UOM to packs (American standard)"""
        if self.is_cigarette:
            self.is_cigar = False
            self.is_tobacco = False
            # Use the predefined pack UOM (American standard: 20 cigarettes per pack)
            pack_uom = self.env.ref('ai_ci_to_invoice.uom_pack', raise_if_not_found=False)
            if pack_uom:
                self.uom_id = pack_uom.id
                self.uom_po_id = pack_uom.id

    @api.onchange('is_tobacco')
    def _onchange_is_tobacco(self):
        """When tobacco is selected, uncheck others and set UOM to fluid ounces (American standard)"""
        if self.is_tobacco:
            self.is_cigar = False
            self.is_cigarette = False
            # Use the predefined fl oz UOM (American standard)
            fl_oz_uom = self.env.ref('ai_ci_to_invoice.uom_fl_oz', raise_if_not_found=False)
            if fl_oz_uom:
                self.uom_id = fl_oz_uom.id
                self.uom_po_id = fl_oz_uom.id

    def get_product_sort_key(self):
        """Return sort key for product ordering: premium cigars=1, cigarettes=2, tobacco=3, non tobacco=4, disposable=5"""
        if self.is_cigar:
            return 1
        elif self.is_cigarette:
            return 2
        elif self.is_tobacco:
            return 3
        elif self.is_disposable_category():
            return 5  # Disposable products come after non-tobacco
        else:
            return 4  # Non tobacco products

    def is_disposable_category(self):
        """Check if product belongs to disposable category"""
        return self.categ_id and 'disposable' in self.categ_id.name.lower()


class ProductProduct(models.Model):
    _inherit = 'product.product'

    is_cigar = fields.Boolean(
        string='Is Premium Cigar',
        related='product_tmpl_id.is_cigar',
        store=True,
        readonly=False
    )

    is_cigarette = fields.Boolean(
        string='Is Cigarette',
        related='product_tmpl_id.is_cigarette',
        store=True,
        readonly=False
    )

    is_tobacco = fields.Boolean(
        string='Is Tobacco',
        related='product_tmpl_id.is_tobacco',
        store=True,
        readonly=False
    )

    def get_product_sort_key(self):
        """Return sort key for product ordering: premium cigars=1, cigarettes=2, tobacco=3, non tobacco=4, disposable=5"""
        if self.is_cigar:
            return 1
        elif self.is_cigarette:
            return 2
        elif self.is_tobacco:
            return 3
        elif self.is_disposable_category():
            return 5  # Disposable products come after non-tobacco
        else:
            return 4  # Non tobacco products

    def is_disposable_category(self):
        """Check if product belongs to disposable category"""
        return self.categ_id and 'disposable' in self.categ_id.name.lower()

    def get_product_type_name(self):
        """Return product type name for grouping"""
        if self.is_cigar:
            return 'Premium Cigars'
        elif self.is_cigarette:
            return 'Cigarettes'
        elif self.is_tobacco:
            return 'Tobacco Products'
        elif self.is_disposable_category():
            return 'Disposable Products'
        else:
            return 'Non Tobacco Products'
