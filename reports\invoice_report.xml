<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Invoice Report with Product Type Grouping -->
        <template id="report_invoice_document_enhanced" inherit_id="account.report_invoice_document">
            <xpath expr="//table[@name='invoice_line_table']" position="replace">
                <table class="table table-sm o_main_table" name="invoice_line_table">
                    <thead>
                        <tr>
                            <th name="th_description" class="text-left"><span>No.</span></th>
                            <th name="th_description" class="text-left"><span>SKU</span></th>
                            <th name="th_description" class="text-left"><span>Description</span></th>
                            <th name="th_quantity" class="text-right"><span>Quantity</span></th>
                            <th name="th_volume" class="text-right"><span>Vol (ml)</span></th>
                            <th name="th_total_volume" class="text-right"><span>Total Vol (ml)</span></th>
                            <th name="th_priceunit" class="text-right"><span>Price</span></th>
                            <th name="th_price_unit" class="text-right"><span>Amount</span></th>
                        </tr>
                    </thead>
                    <tbody class="invoice_tbody">
                        <!-- Set current_subtotal to prevent base template errors -->
                        <t t-set="current_subtotal" t-value="0"/>

                        <!-- PREMIUM CIGARS Section -->
                        <t t-set="cigar_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigar)"/>
                        <t t-if="cigar_lines">
                            <!-- Products first -->
                            <t t-foreach="cigar_lines" t-as="line">
                                <tr>
                                    <td><span t-esc="line_index + 1"/></td>
                                    <td><span t-field="line.product_id.barcode"/></td>
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right"><span t-field="line.quantity"/></td>
                                    <td class="text-right"><span t-field="line.product_id.x_volume"/></td>
                                    <td class="text-right"><span t-esc="(line.quantity * (line.product_id.x_volume or 0))"/></td>
                                    <td class="text-right"><span t-field="line.price_unit"/></td>
                                    <td class="text-right"><span t-field="line.price_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></td>
                                </tr>
                            </t>
                            <!-- Sub-classification header with totals -->
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>PREMIUM CIGARS</strong>
                                </td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="cigar_qty" t-value="sum(cigar_lines.mapped('quantity')) if cigar_lines else 0"/>
                                    <strong><span t-esc="cigar_qty"/></strong>
                                </td>
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;"></td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="cigar_total" t-value="sum(cigar_lines.mapped('price_subtotal')) if cigar_lines else 0"/>
                                    <strong><span t-esc="cigar_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></strong>
                                </td>
                            </tr>
                        </t>

                        <!-- CIGARETTES Section (Temporarily Commented) -->

                        <t t-set="cigarette_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigarette)"/>
                        <t t-if="cigarette_lines">
                            <!-- Products first -->
                            <t t-foreach="cigarette_lines" t-as="line">
                                <tr>
                                    <td><span t-esc="line_index + 1"/></td>
                                    <td><span t-field="line.product_id.barcode"/></td>
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right"><span t-field="line.quantity"/></td>
                                    <td class="text-right"><span t-field="line.product_id.x_volume"/></td>
                                    <td class="text-right"><span t-esc="(line.quantity * (line.product_id.x_volume or 0))"/></td>
                                    <td class="text-right"><span t-field="line.price_unit"/></td>
                                    <td class="text-right"><span t-field="line.price_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></td>
                                </tr>
                            </t>
                            <!-- Sub-classification header with totals -->
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>CIGARETTES</strong>
                                </td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="cigarette_qty" t-value="sum(cigarette_lines.mapped('quantity')) if cigarette_lines else 0"/>
                                    <strong><span t-esc="cigarette_qty"/></strong>
                                </td>
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;"></td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="cigarette_total" t-value="sum(cigarette_lines.mapped('price_subtotal')) if cigarette_lines else 0"/>
                                    <strong><span t-esc="cigarette_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></strong>
                                </td>
                            </tr>
                        </t>


                        <!-- TOBACCO Section -->
                        <t t-set="tobacco_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_tobacco)"/>
                        <t t-if="tobacco_lines">
                            <!-- Products first -->
                            <t t-foreach="tobacco_lines" t-as="line">
                                <tr>
                                    <td><span t-esc="line_index + 1"/></td>
                                    <td><span t-field="line.product_id.barcode"/></td>
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right"><span t-field="line.quantity"/></td>
                                    <td class="text-right"><span t-field="line.product_id.x_volume"/></td>
                                    <td class="text-right"><span t-esc="(line.quantity * (line.product_id.x_volume or 0))"/></td>
                                    <td class="text-right"><span t-field="line.price_unit"/></td>
                                    <td class="text-right"><span t-field="line.price_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></td>
                                </tr>
                            </t>
                            <!-- Sub-classification header with totals -->
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>TOBACCO PRODUCTS</strong>
                                </td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="tobacco_qty" t-value="sum(tobacco_lines.mapped('quantity')) if tobacco_lines else 0"/>
                                    <strong><span t-esc="tobacco_qty"/></strong>
                                </td>
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;"></td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="tobacco_total" t-value="sum(tobacco_lines.mapped('price_subtotal')) if tobacco_lines else 0"/>
                                    <strong><span t-esc="tobacco_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></strong>
                                </td>
                            </tr>
                        </t>

                        <!-- NON TOBACCO PRODUCTS Section -->
                        <t t-set="other_lines" t-value="o.invoice_line_ids.filtered(lambda line: not line.product_id or (not line.product_id.is_cigar and not line.product_id.is_cigarette and not line.product_id.is_tobacco))"/>
                        <t t-if="other_lines">
                            <!-- Products first -->
                            <t t-foreach="other_lines" t-as="line">
                                <tr>
                                    <td><span t-esc="line_index + 1"/></td>
                                    <td><span t-field="line.product_id.barcode"/></td>
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right"><span t-field="line.quantity"/></td>
                                    <td class="text-right"><span t-field="line.product_id.x_volume"/></td>
                                    <td class="text-right"><span t-esc="(line.quantity * (line.product_id.x_volume or 0))"/></td>
                                    <td class="text-right"><span t-field="line.price_unit"/></td>
                                    <td class="text-right"><span t-field="line.price_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></td>
                                </tr>
                            </t>
                            <!-- Sub-classification header with totals -->
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>NON TOBACCO PRODUCTS</strong>
                                </td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="other_qty" t-value="sum(other_lines.mapped('quantity')) if other_lines else 0"/>
                                    <strong><span t-esc="other_qty"/></strong>
                                </td>
                                <td colspan="3" style="border-top: 2px solid #dee2e6; padding: 8px;"></td>
                                <td class="text-right" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <t t-set="other_total" t-value="sum(other_lines.mapped('price_subtotal')) if other_lines else 0"/>
                                    <strong><span t-esc="other_total" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/></strong>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </xpath>

            <!-- Add Product Type Summary Section after the main table -->
            <xpath expr="//table[@name='invoice_line_table']" position="after">
                <div class="clearfix" style="margin-top: 20px;">
                    <div class="row">
                        <div class="col-6">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th class="text-right">Quantity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="cigar_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigar)"/>
                                    <t t-set="cigar_qty" t-value="sum(cigar_lines_summary.mapped('quantity')) if cigar_lines_summary else 0"/>
                                    <t t-if="cigar_qty > 0">
                                        <tr>
                                            <td>PREMIUM CIGAR</td>
                                            <td class="text-right"><span t-esc="cigar_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="cigarette_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigarette)"/>
                                    <t t-set="cigarette_qty" t-value="sum(cigarette_lines_summary.mapped('quantity')) if cigarette_lines_summary else 0"/>
                                    <t t-if="cigarette_qty > 0">
                                        <tr>
                                            <td>CIGARETTE</td>
                                            <td class="text-right"><span t-esc="cigarette_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="tobacco_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_tobacco)"/>
                                    <t t-set="tobacco_qty" t-value="sum(tobacco_lines_summary.mapped('quantity')) if tobacco_lines_summary else 0"/>
                                    <t t-if="tobacco_qty > 0">
                                        <tr>
                                            <td>TOBACCO</td>
                                            <td class="text-right"><span t-esc="tobacco_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="other_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: not line.product_id or (not line.product_id.is_cigar and not line.product_id.is_cigarette and not line.product_id.is_tobacco))"/>
                                    <t t-set="other_qty" t-value="sum(other_lines_summary.mapped('quantity')) if other_lines_summary else 0"/>
                                    <t t-if="other_qty > 0">
                                        <tr>
                                            <td>NON TOBACCO</td>
                                            <td class="text-right"><span t-esc="other_qty"/></td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>



    </data>
</odoo>
